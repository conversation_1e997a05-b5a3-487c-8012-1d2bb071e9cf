"""
统一脚本管理器
提供Web界面统一管理所有数据获取脚本
"""

import os
import json
import subprocess
import threading
from datetime import datetime
from flask import Flask, render_template, request, jsonify
import webbrowser
import time

app = Flask(__name__)

class UnifiedScriptManager:
    """统一脚本管理器"""
    
    def __init__(self):
        self.scripts = {
            '5fen': {
                'name': '5分钟K线数据',
                'file': '5fen.py',
                'description': '获取9:35-9:45时间段的5分钟K线收盘价',
                'status': 'stopped'
            },
            'trading': {
                'name': '日线交易数据',
                'file': 'trading.py', 
                'description': '获取股票日线交易数据',
                'status': 'stopped'
            },
            'all_stocks': {
                'name': '全量股票数据',
                'file': 'all_stocks_data_optimized.py',
                'description': '批量获取优化的股票数据',
                'status': 'stopped'
            },
            'index_weight': {
                'name': '指数权重数据',
                'file': 'index_weight_fetcher.py',
                'description': '获取指数成分股权重数据',
                'status': 'stopped'
            },
            'sw_industry': {
                'name': '申万行业数据',
                'file': 'sw_industry_fetcher.py',
                'description': '获取申万行业分类数据',
                'status': 'stopped'
            }
        }
        self.running_processes = {}
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置"""
        default_config = {
            'date_range': {
                'start_date': '2024-01-01',
                'end_date': '2024-12-31'
            },
            'time_range': {
                'start_time': '09:35',
                'end_time': '09:45'
            },
            'trade_days_count': 250,
            'delay': 0.2
        }
        
        config_file = 'unified_config.json'
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return default_config
    
    def save_config(self, config):
        """保存配置"""
        try:
            with open('unified_config.json', 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True, "配置保存成功"
        except Exception as e:
            return False, f"配置保存失败: {str(e)}"
    
    def run_script(self, script_key):
        """运行脚本"""
        if script_key not in self.scripts:
            return False, "脚本不存在"
        
        script_info = self.scripts[script_key]
        script_file = script_info['file']
        
        if not os.path.exists(script_file):
            return False, f"脚本文件不存在: {script_file}"
        
        try:
            # 启动脚本进程
            process = subprocess.Popen(
                ['python', script_file],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.running_processes[script_key] = process
            self.scripts[script_key]['status'] = 'running'
            
            return True, f"脚本 {script_info['name']} 启动成功"
        
        except Exception as e:
            return False, f"启动脚本失败: {str(e)}"
    
    def stop_script(self, script_key):
        """停止脚本"""
        if script_key in self.running_processes:
            try:
                process = self.running_processes[script_key]
                process.terminate()
                del self.running_processes[script_key]
                self.scripts[script_key]['status'] = 'stopped'
                return True, f"脚本已停止"
            except Exception as e:
                return False, f"停止脚本失败: {str(e)}"
        return False, "脚本未运行"
    
    def get_script_status(self, script_key):
        """获取脚本状态"""
        if script_key in self.running_processes:
            process = self.running_processes[script_key]
            if process.poll() is None:
                return 'running'
            else:
                # 进程已结束
                del self.running_processes[script_key]
                self.scripts[script_key]['status'] = 'stopped'
                return 'stopped'
        return 'stopped'

manager = UnifiedScriptManager()

@app.route('/')
def index():
    """主页"""
    return render_template('unified_manager.html', 
                         scripts=manager.scripts, 
                         config=manager.config)

@app.route('/update_config', methods=['POST'])
def update_config():
    """更新配置"""
    try:
        new_config = {
            'date_range': {
                'start_date': request.form['start_date'],
                'end_date': request.form['end_date']
            },
            'time_range': {
                'start_time': request.form['start_time'],
                'end_time': request.form['end_time']
            },
            'trade_days_count': int(request.form['trade_days_count']),
            'delay': float(request.form['delay'])
        }
        
        success, message = manager.save_config(new_config)
        manager.config = new_config
        
        return jsonify({
            'success': success,
            'message': message
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        })

@app.route('/run_script/<script_key>', methods=['POST'])
def run_script(script_key):
    """运行脚本"""
    success, message = manager.run_script(script_key)
    return jsonify({
        'success': success,
        'message': message
    })

@app.route('/stop_script/<script_key>', methods=['POST'])
def stop_script(script_key):
    """停止脚本"""
    success, message = manager.stop_script(script_key)
    return jsonify({
        'success': success,
        'message': message
    })

@app.route('/get_status')
def get_status():
    """获取所有脚本状态"""
    status = {}
    for key in manager.scripts:
        status[key] = manager.get_script_status(key)
        manager.scripts[key]['status'] = status[key]
    
    return jsonify({
        'scripts': manager.scripts,
        'config': manager.config
    })

def create_html_template():
    """创建HTML模板"""
    template_dir = 'templates'
    if not os.path.exists(template_dir):
        os.makedirs(template_dir)

    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一脚本管理器</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .config-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
        }
        .config-item {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .scripts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .script-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }
        .script-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .script-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.running {
            background: #d4edda;
            color: #155724;
        }
        .status.stopped {
            background: #f8d7da;
            color: #721c24;
        }
        .script-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .script-actions {
            display: flex;
            gap: 10px;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            display: none;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎛️ 统一脚本管理器</h1>

        <div id="alert" class="alert"></div>

        <!-- 配置区域 -->
        <div class="config-section">
            <h3>⚙️ 全局配置</h3>
            <form id="configForm">
                <div class="config-row">
                    <div class="config-item">
                        <label>开始日期:</label>
                        <input type="date" name="start_date" value="{{ config.date_range.start_date }}">
                    </div>
                    <div class="config-item">
                        <label>结束日期:</label>
                        <input type="date" name="end_date" value="{{ config.date_range.end_date }}">
                    </div>
                    <div class="config-item">
                        <label>开始时间:</label>
                        <input type="time" name="start_time" value="{{ config.time_range.start_time }}">
                    </div>
                    <div class="config-item">
                        <label>结束时间:</label>
                        <input type="time" name="end_time" value="{{ config.time_range.end_time }}">
                    </div>
                </div>
                <div class="config-row">
                    <div class="config-item">
                        <label>交易日数量:</label>
                        <input type="number" name="trade_days_count" value="{{ config.trade_days_count }}" min="1" max="500">
                    </div>
                    <div class="config-item">
                        <label>延时(秒):</label>
                        <input type="number" name="delay" value="{{ config.delay }}" step="0.1" min="0.1" max="5">
                    </div>
                    <div class="config-item">
                        <button type="submit" class="btn-success">💾 保存配置</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 脚本管理区域 -->
        <div class="scripts-section">
            <h3>📜 脚本管理</h3>
            <div class="scripts-grid">
                {% for key, script in scripts.items() %}
                <div class="script-card">
                    <div class="script-header">
                        <div class="script-name">{{ script.name }}</div>
                        <div class="status {{ script.status }}">{{ script.status }}</div>
                    </div>
                    <div class="script-description">{{ script.description }}</div>
                    <div class="script-actions">
                        <button onclick="runScript('{{ key }}')" class="btn-primary">▶️ 运行</button>
                        <button onclick="stopScript('{{ key }}')" class="btn-danger">⏹️ 停止</button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <script>
        // 配置保存
        document.getElementById('configForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);

            fetch('/update_config', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, data.success ? 'success' : 'danger');
            });
        });

        // 运行脚本
        function runScript(scriptKey) {
            fetch(`/run_script/${scriptKey}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, data.success ? 'success' : 'danger');
                if (data.success) {
                    updateStatus();
                }
            });
        }

        // 停止脚本
        function stopScript(scriptKey) {
            fetch(`/stop_script/${scriptKey}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                showAlert(data.message, data.success ? 'success' : 'danger');
                if (data.success) {
                    updateStatus();
                }
            });
        }

        // 更新状态
        function updateStatus() {
            fetch('/get_status')
            .then(response => response.json())
            .then(data => {
                // 更新状态显示
                for (const [key, script] of Object.entries(data.scripts)) {
                    const statusElement = document.querySelector(`[data-script="${key}"] .status`);
                    if (statusElement) {
                        statusElement.textContent = script.status;
                        statusElement.className = `status ${script.status}`;
                    }
                }
            });
        }

        // 显示提示
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }

        // 定期更新状态
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>'''

    with open(os.path.join(template_dir, 'unified_manager.html'), 'w', encoding='utf-8') as f:
        f.write(html_content)

def open_browser():
    """延迟打开浏览器"""
    time.sleep(1.5)
    webbrowser.open('http://localhost:5001')

def main():
    """主函数"""
    print("🚀 启动统一脚本管理器...")
    
    # 创建HTML模板
    create_html_template()
    
    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    print("📱 Web界面将在浏览器中打开...")
    print("🌐 访问地址: http://localhost:5001")
    print("⚠️  按 Ctrl+C 停止服务器")
    
    # 启动Flask应用
    app.run(debug=False, host='localhost', port=5001)

if __name__ == '__main__':
    main()
